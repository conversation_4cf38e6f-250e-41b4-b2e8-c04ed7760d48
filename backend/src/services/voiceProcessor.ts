import { VoiceStorageQueueModel } from '../database/models/VoiceStorageQueue.js';
import { AudioDataModel } from '../database/models/AudioData.js';
import { voiceStorageService } from './voiceStorage.js';
import type { VoiceStorageQueueItem } from '../database/models/VoiceStorageQueue.js';
import type { ConversationMessage } from '../../../shared/types.js';

export interface AudioCaptureData {
  messageId?: string; // Made optional for raw audio chunks
  userId: string;
  sessionId: string;
  role: 'user' | 'assistant';
  audioData: string; // base64 encoded
  mimeType: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export class VoiceProcessorService {
  private processingInterval: NodeJS.Timeout | null = null;
  private isProcessing = false;
  private readonly PROCESSING_INTERVAL_MS = 2000; // Process queue every 2 seconds
  private readonly BATCH_SIZE = 5; // Process up to 5 items at once

  constructor() {
    console.log('🎵 VoiceProcessorService initialized');
  }

  /**
   * Initialize the voice processor and start background processing
   */
  async initialize(): Promise<void> {
    try {
      // Initialize voice storage service
      await voiceStorageService.initialize();
      
      // Start background processing
      this.startBackgroundProcessing();
      
      console.log('✅ VoiceProcessorService initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing VoiceProcessorService:', error);
      throw error;
    }
  }

  /**
   * Queue audio data for background storage processing
   * This method is designed to be non-blocking for real-time UX
   */
  async queueAudioForStorage(audioData: AudioCaptureData): Promise<void> {
    try {
      console.log(`🎤 Queuing audio for storage: ${audioData.role} message ${audioData.messageId}`);
      
      // Add to processing queue - this is fast and non-blocking
      await VoiceStorageQueueModel.create({
        messageId: audioData.messageId,
        userId: audioData.userId,
        sessionId: audioData.sessionId,
        role: audioData.role,
        audioData: audioData.audioData,
        mimeType: audioData.mimeType,
        priority: audioData.role === 'user' ? 3 : 5, // Prioritize user audio slightly
        metadata: {
          timestamp: audioData.timestamp?.toISOString() || new Date().toISOString(),
          ...audioData.metadata
        }
      });

      console.log(`✅ Audio queued successfully for ${audioData.role} message ${audioData.messageId}`);
    } catch (error) {
      console.error('❌ Error queuing audio for storage:', error);
      // Don't throw error to avoid affecting real-time conversation
    }
  }

  /**
   * Process user audio input (called from WebSocket handler)
   */
  async processUserAudio(audioData: AudioCaptureData): Promise<void> {
    // Queue for background storage without blocking
    await this.queueAudioForStorage(audioData);
  }

  /**
   * Process assistant audio output (called from Hume message handler)
   */
  async processAssistantAudio(audioData: AudioCaptureData): Promise<void> {
    // Queue for background storage without blocking
    await this.queueAudioForStorage(audioData);
  }

  /**
   * Start background processing of the voice storage queue
   */
  private startBackgroundProcessing(): void {
    if (this.processingInterval) {
      return; // Already started
    }

    console.log('🔄 Starting background voice processing...');
    
    this.processingInterval = setInterval(async () => {
      if (this.isProcessing) {
        return; // Skip if already processing
      }

      try {
        await this.processQueue();
      } catch (error) {
        console.error('❌ Error in background voice processing:', error);
      }
    }, this.PROCESSING_INTERVAL_MS);
  }

  /**
   * Stop background processing
   */
  stopBackgroundProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      console.log('⏹️ Background voice processing stopped');
    }
  }

  /**
   * Process items in the voice storage queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      // Get pending items from queue
      const pendingItems = await VoiceStorageQueueModel.findPendingItems(this.BATCH_SIZE);
      
      if (pendingItems.length === 0) {
        return; // No items to process
      }

      console.log(`🔄 Processing ${pendingItems.length} voice storage items...`);

      // Process items in parallel for better performance
      const processingPromises = pendingItems.map(item => this.processQueueItem(item));
      await Promise.allSettled(processingPromises);

      console.log(`✅ Completed processing ${pendingItems.length} voice storage items`);
    } catch (error) {
      console.error('❌ Error processing voice storage queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process a single queue item
   */
  private async processQueueItem(item: VoiceStorageQueueItem): Promise<void> {
    try {
      console.log(`🎵 Processing voice storage item: ${item.id} (${item.role})`);

      // Mark as processing
      await VoiceStorageQueueModel.markAsProcessing(item.id);

      // Upload to cloud storage
      const voiceMetadata = await voiceStorageService.uploadVoiceData({
        userId: item.userId,
        sessionId: item.sessionId,
        messageId: item.messageId,
        role: item.role,
        audioData: item.audioData,
        mimeType: item.mimeType,
        timestamp: new Date(item.metadata?.timestamp || item.createdAt),
        metadata: item.metadata
      });

      // Save audio data record to database
      await AudioDataModel.createFromVoiceMetadata(voiceMetadata);

      // Mark as completed
      await VoiceStorageQueueModel.markAsCompleted(item.id);

      console.log(`✅ Successfully processed voice storage item: ${item.id}`);
    } catch (error) {
      console.error(`❌ Error processing voice storage item ${item.id}:`, error);
      
      // Mark as failed (will retry if under max retries)
      await VoiceStorageQueueModel.markAsFailed(
        item.id, 
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  /**
   * Get processing statistics
   */
  async getProcessingStats(): Promise<{
    queue: any;
    storage: any;
    isProcessing: boolean;
  }> {
    try {
      const [queueStats, storageStats] = await Promise.all([
        VoiceStorageQueueModel.getQueueStats(),
        AudioDataModel.getStorageStats()
      ]);

      return {
        queue: queueStats,
        storage: storageStats,
        isProcessing: this.isProcessing
      };
    } catch (error) {
      console.error('❌ Error getting processing stats:', error);
      return {
        queue: { pending: 0, processing: 0, completed: 0, failed: 0, retrying: 0, total: 0 },
        storage: { totalFiles: 0, totalSize: 0, userFiles: 0, assistantFiles: 0 },
        isProcessing: this.isProcessing
      };
    }
  }

  /**
   * Manually trigger queue processing (for testing/admin)
   */
  async triggerProcessing(): Promise<void> {
    console.log('🔄 Manually triggering voice processing...');
    await this.processQueue();
  }

  /**
   * Clean up old completed queue items
   */
  async cleanupQueue(olderThanDays: number = 7): Promise<number> {
    try {
      const deletedCount = await VoiceStorageQueueModel.cleanupCompleted(olderThanDays);
      console.log(`🧹 Cleaned up ${deletedCount} completed queue items older than ${olderThanDays} days`);
      return deletedCount;
    } catch (error) {
      console.error('❌ Error cleaning up queue:', error);
      return 0;
    }
  }

  /**
   * Retry failed items
   */
  async retryFailedItems(): Promise<number> {
    try {
      const failedItems = await VoiceStorageQueueModel.findByStatus('failed', 100);
      let retriedCount = 0;

      for (const item of failedItems) {
        if (item.retryCount < item.maxRetries) {
          await VoiceStorageQueueModel.updateStatus(item.id, {
            status: 'retrying',
            errorMessage: undefined
          });
          retriedCount++;
        }
      }

      console.log(`🔄 Retried ${retriedCount} failed voice storage items`);
      return retriedCount;
    } catch (error) {
      console.error('❌ Error retrying failed items:', error);
      return 0;
    }
  }

  /**
   * Get health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    isProcessing: boolean;
    backgroundProcessingActive: boolean;
  } {
    return {
      status: this.processingInterval ? 'healthy' : 'degraded',
      isProcessing: this.isProcessing,
      backgroundProcessingActive: this.processingInterval !== null
    };
  }
}

// Create singleton instance
export const voiceProcessorService = new VoiceProcessorService();
