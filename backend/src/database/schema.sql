-- Database schema for ORA Hume EVI application

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    google_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    profile_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Chat sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    hume_chat_group_id VARCHAR(255),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'error', 'interrupted')),
    metadata JSONB DEFAULT '{}'
);

-- Conversation messages table
CREATE TABLE IF NOT EXISTS conversation_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    hume_message_id VARCHAR(255),
    emotions JSONB DEFAULT '{}',
    prosody_scores JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}'
);

-- Audio data table for voice storage
CREATE TABLE IF NOT EXISTS audio_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES conversation_messages(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    storage_url VARCHAR(500) NOT NULL,
    signed_url VARCHAR(1000),
    signed_url_expires_at TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- in milliseconds
    file_size INTEGER NOT NULL, -- in bytes
    mime_type VARCHAR(100) NOT NULL,
    storage_provider VARCHAR(50) DEFAULT 'gcs', -- 'gcs', 's3', etc.
    upload_status VARCHAR(20) DEFAULT 'pending' CHECK (upload_status IN ('pending', 'uploading', 'completed', 'failed')),
    upload_error TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for audio_data table
CREATE INDEX IF NOT EXISTS idx_audio_data_message_id ON audio_data(message_id);
CREATE INDEX IF NOT EXISTS idx_audio_data_user_id ON audio_data(user_id);
CREATE INDEX IF NOT EXISTS idx_audio_data_session_id ON audio_data(session_id);
CREATE INDEX IF NOT EXISTS idx_audio_data_role ON audio_data(role);
CREATE INDEX IF NOT EXISTS idx_audio_data_upload_status ON audio_data(upload_status);
CREATE INDEX IF NOT EXISTS idx_audio_data_created_at ON audio_data(created_at);

-- Voice storage queue table for async processing
CREATE TABLE IF NOT EXISTS voice_storage_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES conversation_messages(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
    audio_data TEXT NOT NULL, -- base64 encoded audio
    mime_type VARCHAR(100) NOT NULL,
    priority INTEGER DEFAULT 5, -- 1 = highest, 10 = lowest
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'retrying')),
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for voice_storage_queue table
CREATE INDEX IF NOT EXISTS idx_voice_queue_status ON voice_storage_queue(status);
CREATE INDEX IF NOT EXISTS idx_voice_queue_priority ON voice_storage_queue(priority);
CREATE INDEX IF NOT EXISTS idx_voice_queue_created_at ON voice_storage_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_voice_queue_user_id ON voice_storage_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_voice_queue_session_id ON voice_storage_queue(session_id);

-- Sessions table for express-session
CREATE TABLE IF NOT EXISTS session (
    sid VARCHAR NOT NULL COLLATE "default" PRIMARY KEY,
    sess JSON NOT NULL,
    expire TIMESTAMP(6) NOT NULL
) WITH (OIDS=FALSE);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_status ON chat_sessions(status);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_started_at ON chat_sessions(started_at);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_session_id ON conversation_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_timestamp ON conversation_messages(timestamp);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_role ON conversation_messages(role);
CREATE INDEX IF NOT EXISTS idx_audio_data_message_id ON audio_data(message_id);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views for analytics (optional)
CREATE OR REPLACE VIEW user_session_stats AS
SELECT 
    u.id as user_id,
    u.name,
    u.email,
    COUNT(cs.id) as total_sessions,
    AVG(EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))) as avg_session_duration,
    MAX(cs.started_at) as last_session_at,
    COUNT(cm.id) as total_messages
FROM users u
LEFT JOIN chat_sessions cs ON u.id = cs.user_id
LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
GROUP BY u.id, u.name, u.email;

-- View for emotion analytics
CREATE OR REPLACE VIEW emotion_analytics AS
SELECT
    cs.user_id,
    cs.id as session_id,
    cm.role,
    jsonb_object_keys(cm.emotions) as emotion,
    (cm.emotions ->> jsonb_object_keys(cm.emotions))::float as score,
    cm.timestamp
FROM chat_sessions cs
JOIN conversation_messages cm ON cs.id = cm.session_id
WHERE cm.emotions != '{}'::jsonb;

-- Admin whitelist table for access control
CREATE TABLE IF NOT EXISTS admin_whitelist (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    role VARCHAR(50) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin', 'viewer')),
    added_by UUID REFERENCES users(id),
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    last_access TIMESTAMP WITH TIME ZONE,
    permissions JSONB DEFAULT '{}',
    notes TEXT
);

-- Indexes for admin whitelist
CREATE INDEX IF NOT EXISTS idx_admin_whitelist_email ON admin_whitelist(email);
CREATE INDEX IF NOT EXISTS idx_admin_whitelist_active ON admin_whitelist(is_active);
CREATE INDEX IF NOT EXISTS idx_admin_whitelist_role ON admin_whitelist(role);

-- Admin activity log for security and auditing
CREATE TABLE IF NOT EXISTS admin_activity_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_email VARCHAR(255) NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(255),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(255)
);

-- Indexes for admin activity log
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_email ON admin_activity_log(admin_email);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_timestamp ON admin_activity_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_action ON admin_activity_log(action);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_resource ON admin_activity_log(resource);
